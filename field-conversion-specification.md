# Field Conversion Specification for Bidirectional Synchronization

## Executive Summary

This document provides a comprehensive technical specification for converting field values during bidirectional synchronization between AutoPatient (AP) and CliniCore (CC) systems. It defines exact conversion rules, data transformations, validation requirements, and edge case handling for all supported field types.

**Key Principles:**
- **Type-aware conversion**: Respect source and target field types and capabilities
- **Data integrity preservation**: Maintain semantic meaning during conversion
- **Bidirectional compatibility**: Ensure round-trip conversions preserve data
- **Priority-based handling**: Use optimal field types when multiple options exist
- **Graceful degradation**: Provide fallback mechanisms for unsupported scenarios

## Field Type Conversion Matrix

### AutoPatient → CliniCore Conversions

| AP Field Type | CC Field Type | Multi-Value Support | Conversion Logic | Notes |
|---------------|---------------|-------------------|------------------|-------|
| `TEXT` | `text` | ❌ | Direct mapping | Basic text field |
| `LARGE_TEXT` | `textarea` | ❌ | Direct mapping | Large text content |
| `NUMERICAL` | `number` | ❌ | Direct mapping | Numeric values |
| `PHONE` | `telephone` | ❌ | Direct mapping | Phone number format |
| `EMAIL` | `email` | ❌ | Direct mapping | Email validation |
| `DATE` | `date` | ❌ | Direct mapping | ISO date format |
| `MONETORY` | `text` | ❌ | Convert to string | Monetary values as text in CC |
| `SINGLE_OPTIONS` | `select` | ❌ | Map options to allowedValues | Single select dropdown |
| `MULTIPLE_OPTIONS` | `select` | ✅ | Map options to allowedValues | Multi-select dropdown |
| `CHECKBOX` | `select` | ✅ | Map options to allowedValues | Multi-select checkbox |
| `RADIO` | `select` | ❌ | Map options to allowedValues | Radio button group |
| `RADIO` (Yes/No) | `boolean` | ❌ | Special boolean conversion | Yes/Ja→true, No/Nein→false |
| `TEXTBOX_LIST` | `text` | ✅ | Convert Record to multi-value | Multi-value text field |
| `FILE_UPLOAD` | ❌ | ❌ | **SKIPPED** | Not supported in CC |

### CliniCore → AutoPatient Conversions

| CC Field Type | AP Field Type | Multi-Value Handling | Conversion Logic | Notes |
|---------------|---------------|-------------------|------------------|-------|
| `text` | `TEXT` | Single value | Direct mapping | Basic text field |
| `text` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value text |
| `text` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `textarea` | `LARGE_TEXT` | Single value | Direct mapping | Large text content |
| `textarea` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value |
| `textarea` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `number` | `NUMERICAL` | Single value | Direct mapping | Numeric values |
| `number` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value |
| `number` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `telephone` | `PHONE` | Single value | Direct mapping | Phone number format |
| `telephone` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value |
| `telephone` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `email` | `EMAIL` | Single value | Direct mapping | Email validation |
| `email` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value |
| `email` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `date` | `DATE` | Single value | Direct mapping | ISO date format |
| `date` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | **Preferred** for multi-value |
| `date` (multi) | `TEXT` | Fallback | Join with ` \| ` separator | When no TEXTBOX_LIST match |
| `select` | `SINGLE_OPTIONS` | Single value | Map allowedValues to options | Single select dropdown |
| `select` (multi) | `MULTIPLE_OPTIONS` | ✅ | Map allowedValues to options | **Preferred** for semantic accuracy |
| `select` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | Fallback option |
| `select-or-custom` | `SINGLE_OPTIONS` | Single value | Map allowedValues to options | Single select with custom |
| `select-or-custom` (multi) | `MULTIPLE_OPTIONS` | ✅ | Map allowedValues to options | **Preferred** for semantic accuracy |
| `select-or-custom` (multi) | `TEXTBOX_LIST` | ✅ | Convert to Record<string, string> | Fallback option |
| `boolean` | `RADIO` | ❌ | Create Yes/Ja/No/Nein options | true→"Yes", false→"No" |
| `medication` | `TEXT` | ❌ | Fallback conversion | Medical field to text |
| `permanent-diagnoses` | `TEXT` | ❌ | Fallback conversion | Diagnosis field to text |
| `patient-has-recommended` | `TEXT` | ❌ | Fallback conversion | Recommendation field to text |

## Value Conversion Specifications

### TEXTBOX_LIST Value Conversion

**AP TEXTBOX_LIST Structure:**
```typescript
// AP TEXTBOX_LIST uses field_value object with option IDs as keys
{
  field_value: {
    "option_id_1": "value1",
    "option_id_2": "value2",
    "option_id_3": "value3"
  }
}
```

**CC Multi-Value Structure:**
```typescript
// CC multi-value fields use array of objects
{
  values: [
    { id: "uuid1", value: "value1" },
    { id: "uuid2", value: "value2" },
    { id: "uuid3", value: "value3" }
  ]
}
```

**Conversion Algorithm (CC → AP TEXTBOX_LIST):**
1. Fetch AP field's `picklistOptions` to get option ID mappings
2. For each CC value, find matching AP option by value comparison
3. Create Record<string, string> with option IDs as keys
4. **Complete replacement**: Replace all existing values (not append)
5. **Fresh data**: Always fetch current CC data, don't use cached values

**Conversion Algorithm (AP TEXTBOX_LIST → CC):**
1. Extract values from AP `field_value` object
2. Map option IDs to actual values using `picklistOptions`
3. Create CC multi-value array structure
4. Validate against CC field's `allowedValues` if present

### Multi-Value to TEXT Fallback Conversion

**When to Use:**
- CC multi-value field maps to existing AP TEXT field (not TEXTBOX_LIST)
- No suitable TEXTBOX_LIST field exists for mapping

**Conversion Logic (CC Multi-Value → AP TEXT):**
```typescript
// Join multiple values with pipe separator
const ccValues = ["value1", "value2", "value3"];
const apTextValue = ccValues.join(" | ");
// Result: "value1 | value2 | value3"
```

**Conversion Logic (AP TEXT → CC Multi-Value):**
```typescript
// Split by pipe separator and trim whitespace
const apTextValue = "value1 | value2 | value3";
const ccValues = apTextValue.split("|").map(v => v.trim());
// Result: ["value1", "value2", "value3"]
```

### Boolean Value Conversion

**CC Boolean → AP RADIO:**
```typescript
// Create RADIO field with 4 options for international support
const radioOptions = ["Yes", "Ja", "No", "Nein"];

// Value conversion
const ccBoolean = true;
const apRadioValue = ccBoolean ? "Yes" : "No";
```

**AP RADIO → CC Boolean:**
```typescript
// Detect boolean-style RADIO fields
const yesValues = ["yes", "ja", "true", "1"];
const noValues = ["no", "nein", "false", "0"];

const apRadioValue = "Yes";
const ccBoolean = yesValues.includes(apRadioValue.toLowerCase());
```

### Standard Field Value Transformations

**Phone Number Normalization:**
```typescript
// Remove formatting for cross-platform compatibility
const normalizePhone = (phone: string): string => {
  return phone.replace(/[^\d+]/g, "");
};
```

**Email Validation:**
```typescript
// Basic email validation for sync
const isValidEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};
```

**Date Format Conversion:**
```typescript
// Ensure ISO 8601 format for cross-platform compatibility
const normalizeDate = (date: string): string => {
  return new Date(date).toISOString().split('T')[0];
};
```

## Standard Field Mapping Rules

### CC Custom Fields → AP Standard Fields

| CC Custom Field Name | AP Standard Field | Conversion Notes |
|---------------------|------------------|------------------|
| `phone`, `phone-mobile`, `phoneMobile` | `phone` | Normalize phone format |
| `email` | `email` | Validate email format |
| `firstName`, `first-name` | `firstName` | Direct mapping |
| `lastName`, `last-name` | `lastName` | Direct mapping |
| `dateOfBirth`, `dob` | `dateOfBirth` | ISO date format |
| `gender` | `gender` | Direct mapping |

### AP Custom Fields → CC Standard Fields

| AP Custom Field Name | CC Standard Field | Conversion Notes |
|---------------------|------------------|------------------|
| `firstName` | `firstName` | Direct mapping |
| `lastName` | `lastName` | Direct mapping |
| `email` | `email` | Validate email format |
| `phone` | `phoneMobile` | Normalize phone format |
| `gender` | `gender` | Direct mapping |
| `dateOfBirth`, `dob` | `dob` | ISO date format |

### Special Standard Field Mappings

**AP Standard → CC Custom (Reference Fields):**
- AP Contact ID → CC Custom Field `PatientID`
- AP Contact ID → CC Custom Field `CC Profile` (clickable link: `https://ccdemo.clinicore.eu/patients/{id}/timeline`)

**CC Standard → AP Custom (Reference Fields):**
- CC Patient ID → AP Custom Field `PatientID`
- CC Patient ID → AP Custom Field `AP Profile` (clickable link to AP contact)

## Implementation Guidelines

### Field Creation Rules

1. **AP → CC Direction:**
   - **Never create new CC fields** - only map to existing CC custom fields
   - Log warnings for unmappable AP fields
   - Skip FILE_UPLOAD fields entirely

2. **CC → AP Direction:**
   - **Create new AP fields** when no suitable mapping exists
   - Use intelligent field type selection based on CC field characteristics
   - Handle naming conflicts with suffix (e.g., "email_2")

### Multi-Value Field Priority Logic

**For CC Multi-Value Fields:**
1. **CC select (multi) → AP MULTIPLE_OPTIONS** (preferred for semantic accuracy)
2. **CC select (multi) → AP TEXTBOX_LIST** (fallback)
3. **CC other (multi) → AP TEXTBOX_LIST** (preferred)
4. **CC other (multi) → AP TEXT** (fallback with | separator)

### Option Management

**For Select/Dropdown Fields:**
1. Map CC `allowedValues` to AP `options` array
2. Preserve option order when possible
3. Handle custom values in select-or-custom fields
4. Validate selected values against allowed options

**For TEXTBOX_LIST Fields:**
1. Create `textBoxListOptions` with appropriate labels (Value 1, Value 2, etc.)
2. Use CC `allowedValues` as prefill values when available
3. Provide default empty option when no allowedValues exist

## Edge Cases and Error Handling

### Data Validation

**Empty Value Handling:**
- Filter out empty strings for email/phone fields
- Preserve empty strings for text fields when semantically meaningful
- Handle null/undefined values gracefully

**Type Mismatch Scenarios:**
- Log warnings for incompatible type conversions
- Provide fallback to TEXT field type for unknown types
- Validate numeric values before NUMERICAL field assignment

**Option Value Mismatches:**
- Log warnings when values don't match allowedValues
- Skip invalid options rather than causing API errors
- Preserve valid options and continue processing

### API Error Recovery

**Field Creation Failures:**
- Continue processing other fields when one field creation fails
- Log detailed error information for debugging
- Provide meaningful error messages for common failure scenarios

**Value Sync Failures:**
- Implement retry logic for transient API errors
- Validate data before API calls to prevent errors
- Gracefully handle partial sync scenarios

### Conflict Resolution

**Bidirectional Sync Conflicts:**
- Use timestamp-based conflict resolution when available
- Prefer source platform data during initial sync
- Log conflicts for manual review when automatic resolution isn't possible

**Field Mapping Conflicts:**
- Generate unique field names with numeric suffixes
- Preserve original field intent when possible
- Document mapping decisions for audit trail

## Testing and Validation Requirements

### Unit Test Coverage

**Field Type Conversion:**
- Test all AP → CC field type mappings
- Test all CC → AP field type mappings
- Verify multi-value field handling
- Validate boolean conversion logic

**Value Conversion:**
- Test TEXTBOX_LIST Record<string, string> conversion
- Test multi-value to TEXT fallback with | separator
- Test boolean value mapping (Yes/No/Ja/Nein)
- Test standard field value transformations

**Edge Cases:**
- Test empty value handling
- Test invalid option values
- Test type mismatch scenarios
- Test API error conditions

### Integration Test Scenarios

**End-to-End Sync:**
- Create field in source platform
- Verify field creation in target platform
- Sync field values bidirectionally
- Verify data integrity preservation

**Standard Field Mapping:**
- Test CC custom → AP standard field mapping
- Test AP custom → CC standard field mapping
- Verify conflict resolution for duplicate names

**Multi-Platform Scenarios:**
- Test round-trip conversions (AP → CC → AP)
- Verify semantic meaning preservation
- Test performance with large datasets

## API Integration Specifications

### Required API Endpoints

**AutoPatient API:**
- `GET /custom-fields` - Fetch all custom fields with picklistOptions
- `POST /custom-fields` - Create new custom field with type-specific configuration
- `PUT /contacts/{id}` - Update contact with custom field values
- `GET /contacts/{id}` - Fetch contact with custom field values

**CliniCore API:**
- `GET /custom-fields` - Fetch all custom fields with allowedValues
- `POST /custom-fields` - Create new custom field (if supported)
- `PUT /patients/{id}` - Update patient with custom field values
- `GET /patients/{id}` - Fetch patient with custom field values

### Data Refresh Requirements

**Fresh Data Fetching:**
- Always fetch current field definitions before value sync
- Don't rely on cached picklistOptions or allowedValues
- Refresh field mappings when field definitions change

**Batch Processing:**
- Process multiple field mappings in single API calls when possible
- Implement rate limiting to respect API constraints
- Use efficient pagination for large datasets

### Performance Considerations

**Optimization Strategies:**
- Cache field mappings between sync operations
- Batch field value updates when API supports it
- Use parallel processing for independent field conversions
- Implement incremental sync for large datasets

**Monitoring and Metrics:**
- Track conversion success rates by field type
- Monitor API response times and error rates
- Log performance metrics for optimization
- Alert on conversion failure thresholds

## Detailed Conversion Examples

### Example 1: CC Multi-Value Text → AP TEXTBOX_LIST

**Source (CC):**
```json
{
  "id": "cc_field_123",
  "name": "allergies",
  "type": "text",
  "allowMultipleValues": true,
  "values": [
    { "id": "val1", "value": "Peanuts" },
    { "id": "val2", "value": "Shellfish" },
    { "id": "val3", "value": "Latex" }
  ]
}
```

**Target (AP TEXTBOX_LIST):**
```json
{
  "id": "ap_field_456",
  "name": "Allergies",
  "dataType": "TEXTBOX_LIST",
  "picklistOptions": [
    { "id": "opt1", "value": "Peanuts" },
    { "id": "opt2", "value": "Shellfish" },
    { "id": "opt3", "value": "Latex" }
  ],
  "field_value": {
    "opt1": "Peanuts",
    "opt2": "Shellfish",
    "opt3": "Latex"
  }
}
```

### Example 2: CC Boolean → AP RADIO

**Source (CC):**
```json
{
  "id": "cc_field_789",
  "name": "smoker",
  "type": "boolean",
  "value": true
}
```

**Target (AP RADIO):**
```json
{
  "id": "ap_field_101",
  "name": "Smoker",
  "dataType": "RADIO",
  "options": ["Yes", "Ja", "No", "Nein"],
  "value": "Yes"
}
```

### Example 3: CC Multi-Value → AP TEXT (Fallback)

**Source (CC):**
```json
{
  "id": "cc_field_202",
  "name": "medications",
  "type": "text",
  "allowMultipleValues": true,
  "values": [
    { "id": "med1", "value": "Aspirin 81mg" },
    { "id": "med2", "value": "Lisinopril 10mg" },
    { "id": "med3", "value": "Metformin 500mg" }
  ]
}
```

**Target (AP TEXT - when no TEXTBOX_LIST match):**
```json
{
  "id": "ap_field_303",
  "name": "Current Medications",
  "dataType": "TEXT",
  "value": "Aspirin 81mg | Lisinopril 10mg | Metformin 500mg"
}
```

## Implementation Algorithms

### Algorithm 1: CC Multi-Value to AP TEXTBOX_LIST

```typescript
async function convertCCMultiValueToAPTextboxList(
  ccField: CCCustomField,
  ccValues: CCFieldValue[],
  apField: APCustomField
): Promise<APTextboxListValue> {
  // 1. Fetch fresh AP field data with picklistOptions
  const freshAPField = await apiClient.ap.getCustomField(apField.id);

  // 2. Create option ID to value mapping
  const optionMap = new Map<string, string>();
  freshAPField.picklistOptions?.forEach(option => {
    optionMap.set(option.value.toLowerCase(), option.id);
  });

  // 3. Convert CC values to AP field_value structure
  const fieldValue: Record<string, string> = {};

  for (const ccValue of ccValues) {
    const normalizedValue = ccValue.value.toLowerCase();
    const optionId = optionMap.get(normalizedValue);

    if (optionId) {
      fieldValue[optionId] = ccValue.value;
    } else {
      // Log warning for unmapped value
      console.warn(`CC value "${ccValue.value}" not found in AP options`);
    }
  }

  return { field_value: fieldValue };
}
```

### Algorithm 2: Multi-Value Fallback to TEXT

```typescript
function convertMultiValueToText(values: string[]): string {
  // Filter out empty values and join with pipe separator
  return values
    .filter(value => value && value.trim().length > 0)
    .join(" | ");
}

function convertTextToMultiValue(textValue: string): string[] {
  // Split by pipe separator and clean up whitespace
  return textValue
    .split("|")
    .map(value => value.trim())
    .filter(value => value.length > 0);
}
```

### Algorithm 3: Boolean Conversion

```typescript
function convertCCBooleanToAPRadio(ccBoolean: boolean): string {
  return ccBoolean ? "Yes" : "No";
}

function convertAPRadioToCCBoolean(radioValue: string): boolean {
  const yesValues = ["yes", "ja", "true", "1", "y"];
  return yesValues.includes(radioValue.toLowerCase());
}

function createBooleanRadioOptions(): string[] {
  return ["Yes", "Ja", "No", "Nein"];
}
```

## Validation Rules

### Field Type Compatibility Matrix

```typescript
const FIELD_COMPATIBILITY_MATRIX = {
  // AP → CC compatibility
  "TEXT": ["text", "textarea", "email", "telephone"],
  "LARGE_TEXT": ["textarea", "text"],
  "NUMERICAL": ["number", "text"],
  "PHONE": ["telephone", "text"],
  "EMAIL": ["email", "text"],
  "DATE": ["date", "text"],
  "MONETORY": ["text", "number"],
  "SINGLE_OPTIONS": ["select"],
  "MULTIPLE_OPTIONS": ["select"],
  "CHECKBOX": ["select"],
  "RADIO": ["select", "boolean"],
  "TEXTBOX_LIST": ["text", "textarea", "select"],

  // CC → AP compatibility
  "text": ["TEXT", "LARGE_TEXT", "TEXTBOX_LIST"],
  "textarea": ["LARGE_TEXT", "TEXT", "TEXTBOX_LIST"],
  "number": ["NUMERICAL", "TEXT", "TEXTBOX_LIST"],
  "telephone": ["PHONE", "TEXT", "TEXTBOX_LIST"],
  "email": ["EMAIL", "TEXT", "TEXTBOX_LIST"],
  "date": ["DATE", "TEXT", "TEXTBOX_LIST"],
  "select": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"],
  "select-or-custom": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"],
  "boolean": ["RADIO"],
  "medication": ["TEXT"],
  "permanent-diagnoses": ["TEXT"],
  "patient-has-recommended": ["TEXT"]
};
```

### Value Validation Rules

```typescript
interface ValidationRule {
  fieldType: string;
  validate: (value: any) => ValidationResult;
}

const VALIDATION_RULES: ValidationRule[] = [
  {
    fieldType: "EMAIL",
    validate: (value: string) => ({
      isValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      message: "Invalid email format"
    })
  },
  {
    fieldType: "PHONE",
    validate: (value: string) => ({
      isValid: /^[\d\s\-\+\(\)]+$/.test(value),
      message: "Invalid phone number format"
    })
  },
  {
    fieldType: "NUMERICAL",
    validate: (value: string) => ({
      isValid: !isNaN(Number(value)),
      message: "Value must be numeric"
    })
  },
  {
    fieldType: "DATE",
    validate: (value: string) => ({
      isValid: !isNaN(Date.parse(value)),
      message: "Invalid date format"
    })
  }
];
```

## Error Handling Specifications

### Error Categories and Responses

```typescript
enum ConversionErrorType {
  FIELD_TYPE_MISMATCH = "FIELD_TYPE_MISMATCH",
  INVALID_VALUE_FORMAT = "INVALID_VALUE_FORMAT",
  MISSING_FIELD_MAPPING = "MISSING_FIELD_MAPPING",
  API_ERROR = "API_ERROR",
  VALIDATION_FAILED = "VALIDATION_FAILED",
  OPTION_NOT_FOUND = "OPTION_NOT_FOUND"
}

interface ConversionError {
  type: ConversionErrorType;
  message: string;
  fieldId: string;
  fieldName: string;
  sourceValue?: any;
  suggestions?: string[];
}
```

### Recovery Strategies

1. **Field Type Mismatch:**
   - Attempt fallback to TEXT field type
   - Log warning with suggested field type
   - Continue processing other fields

2. **Invalid Value Format:**
   - Skip invalid values
   - Log validation errors
   - Process valid values only

3. **Missing Field Mapping:**
   - Create new field if direction allows (CC→AP)
   - Skip field if creation not allowed (AP→CC)
   - Log unmapped field for manual review

4. **API Errors:**
   - Implement exponential backoff retry
   - Log detailed error information
   - Continue with next field after max retries

## Performance Optimization Guidelines

### Batch Processing Strategy

```typescript
interface BatchConversionConfig {
  maxBatchSize: number;
  maxConcurrentBatches: number;
  retryAttempts: number;
  retryDelayMs: number;
}

const OPTIMAL_BATCH_CONFIG: BatchConversionConfig = {
  maxBatchSize: 50,
  maxConcurrentBatches: 3,
  retryAttempts: 3,
  retryDelayMs: 1000
};
```

### Caching Strategy

```typescript
interface CacheConfig {
  fieldMappingsTTL: number;    // 1 hour
  picklistOptionsTTL: number;  // 30 minutes
  allowedValuesTTL: number;    // 30 minutes
  maxCacheSize: number;        // 1000 entries
}
```

### Monitoring Metrics

```typescript
interface ConversionMetrics {
  totalFieldsProcessed: number;
  successfulConversions: number;
  failedConversions: number;
  averageProcessingTimeMs: number;
  errorsByType: Record<ConversionErrorType, number>;
  fieldTypeDistribution: Record<string, number>;
}
```

---

**Document Version:** 1.0
**Last Updated:** 2025-08-08
**Status:** Technical Specification - Ready for Implementation

This specification provides comprehensive guidance for implementing bidirectional field synchronization between AutoPatient and CliniCore systems. All conversion rules, algorithms, and edge cases are documented to ensure consistent and reliable data transformation across platforms.
